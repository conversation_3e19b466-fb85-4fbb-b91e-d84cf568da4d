import Dexie from "dexie";

const globalForIndexedDb = window;

const createFileListDbInstance = () => {
  const fileListDbInstance = new Dexie("fileList");
  fileListDbInstance.version(1).stores({
    files: "&id, parentId, name, type",
    covers: "&id",
  });
  if (!globalForIndexedDb.fileListDb) {
    globalForIndexedDb.fileListDb = fileListDbInstance;
  }
  return fileListDbInstance;
};

const createAppDbInstance = () => {
  const appDbInstance = new Dexie("AppDatabase");
  appDbInstance.version(2).stores({
    colorTagData: "&id",
    penData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    rectData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    noteData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
    ocrData: "&uid",
    configData: "&key",
  });

  if (!globalForIndexedDb.appDb) {
    globalForIndexedDb.appDb = appDbInstance;
  }
  return appDbInstance;
};

export const fileListDb =
  globalForIndexedDb.fileListDb ?? createFileListDbInstance();
export const appDb = globalForIndexedDb.appDb ?? createAppDbInstance();
