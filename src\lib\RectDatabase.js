import { appDb as db } from "./AppDatabase";

export async function listShowingRects(fileId) {
  return db.rectData
    .where({ fileId: fileId })
    .and((rect) => !!rect.display)
    .toArray();
}

export async function listFileRects(fileId) {
  return db.rectData.where({ fileId: fileId }).toArray();
}

export async function saveRects(rects) {
  const value = JSON.parse(JSON.stringify(rects));
  if (Array.isArray(value)) {
    return db.rectData.bulkPut(value);
  } else {
    return db.rectData.put(value);
  }
}

export async function updateChildRectsColor(
  currentFileId,
  parentRectId,
  color
) {
  return db.transaction("rw", db.rectData, async () => {
    const parent = await db.rectData.where({ uid: parentRectId }).first();
    if (parent) {
      await db.rectData.put({ ...parent, color });
    }

    const childRects = await db.rectData
      .where({ fileId: currentFileId })
      .filter((r) => r.parentUid === parentRectId)
      .toArray();
    return db.rectData.bulkPut(childRects.map((rect) => ({ ...rect, color })));
  });
}

export async function getRectByUID(uid) {
  return db.rectData.where({ uid }).first();
}

export function deleteRectByUID(uid) {
  return db.rectData.delete(uid);
}

export async function deleteRectByFileIdIndex(fileId, pageIndex) {
  // 删除所有具有指定fileId和pageIndex的矩形，同时将pageIndex大于当前值的矩形的pageIndex减1
  return db.transaction("rw", db.rectData, async () => {
    await db.rectData.where({ fileId, pageIndex }).delete();
    const rects = await db.rectData
      .where({ fileId })
      .and((rect) => rect.pageIndex > pageIndex)
      .toArray();
    return db.rectData.bulkPut(
      rects.map((rect) => ({ ...rect, pageIndex: rect.pageIndex - 1 }))
    );
  });
}

export async function deletePenLinesByFileIdIndex(fileId, pageIndex) {
  // 删除所有具有指定fileId和pageIndex的笔迹，同时将pageIndex大于当前值的笔迹的pageIndex减1
  return db.transaction("rw", db.penData, async () => {
    await db.penData.where({ fileId, pageIndex }).delete();
    const lines = await db.penData
      .where({ fileId })
      .and((line) => line.pageIndex > pageIndex)
      .toArray();
    return db.penData.bulkPut(
      lines.map((line) => ({ ...line, pageIndex: line.pageIndex - 1 }))
    );
  });
}

export async function deleteNoteByFileIdIndex(fileId, pageIndex) {
  // 删除所有具有指定fileId和pageIndex的笔记，同时将pageIndex大于当前值的笔记的pageIndex减1
  return db.transaction("rw", db.noteData, async () => {
    await db.noteData.where({ fileId, pageIndex }).delete();
    const notes = await db.noteData
      .where({ fileId })
      .and((note) => note.pageIndex > pageIndex)
      .toArray();
    return db.noteData.bulkPut(
      notes.map((note) => ({ ...note, pageIndex: note.pageIndex - 1 }))
    );
  });
}

export function createNoteFromRect(fileId, rectId) {
  return {
    fileId: fileId,
    uid: rectId,
    noteTitle: "",
    ocrContent: "",
    noteTags: [],
    noteContentArray: [],
  };
}

export async function getAllNotes() {
  return await db.noteData.toArray();
}

export async function getNoteFromRect(rectId) {
  return await db.noteData.where({ uid: rectId }).first();
}

export async function getNoteFromFile(fileId) {
  return await db.noteData.where({ fileId }).toArray();
}

export async function saveNote(note) {
  const value = JSON.parse(JSON.stringify(note));
  return db.noteData.put(value);
}

export async function getNoteKeywords(keyword) {
  // 寻找noteData中，note.keyword包含keyword的note, 返回[{value: note.keyword}]
  const notes = await db.noteData
    .filter((note) => {
      if (!keyword) {
        return !!note.keyword;
      } else {
        // ignore case
        return note.keyword && note.keyword.toLowerCase().includes(keyword);
      }
    })
    .toArray();
  // remove duplicate
  const keywords = notes.map((note) => note.keyword);
  const uniqueKeywords = [...new Set(keywords)];
  return uniqueKeywords.map((keyword) => ({ value: keyword }));
}

export async function getNoteKeywordsByAlphabet(alphabet) {
  // 寻找noteData中，note.keyword包含alphabet的note, 返回[{value: note.keyword}]
  const notes = await db.noteData
    .filter((note) => {
      if (!alphabet) {
        return !!note.keywordAlphabet;
      } else {
        if (!note.keywordAlphabet) {
          return false;
        }

        if (alphabet === "NUMBER") {
          // keywordAlphabet固定为一个字，如果是数字，则返回true
          return note.keywordAlphabet >= "0" && note.keywordAlphabet <= "9";
        } else {
          return note.keywordAlphabet === alphabet;
        }
      }
    })
    .toArray();
  // remove duplicate
  const keywords = notes.map((note) => note.keyword);
  const uniqueKeywords = [...new Set(keywords)];
  return uniqueKeywords.map((keyword) => ({ value: keyword }));
}

export async function saveOcrData(uid, data, width, height) {
  return await db.ocrData.put({ uid, data, width, height });
}

export async function saveOcrDataAli(uid, data, width, height) {
  return await db.ocrData.put({ uid, aliData: data, width, height });
}

export async function listColorTags() {
  const list = await db.colorTagData.toArray();
  // sort by id
  list.sort((a, b) => a.id - b.id);
  return list;
}

export async function saveColorTags(tags) {
  const value = JSON.parse(JSON.stringify(tags));
  await db.colorTagData.clear();
  return await db.colorTagData.bulkPut(value);
}
